
import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Operation, StatusCarregamentoEnum, StatusDescargaEnum } from '@/types';
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, FileText, Info, MapPin, Package, Truck, User, Receipt, Building, DollarSign, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface OperationDetailsProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  operation: Operation | null;
  onEdit: () => void;
}

const OperationDetails = ({ open, onOpenChange, operation, onEdit }: OperationDetailsProps) => {
  if (!operation) return null;

  // Format date/time from Brazilian format
  const formatDateTime = (dateTimeString?: string) => {
    if (!dateTimeString) return '-';
    return dateTimeString;
  };

  // Get status color
  const getStatusColor = (status?: StatusCarregamentoEnum) => {
    switch (status) {
      case StatusCarregamentoEnum.CARREGADO:
        return 'bg-status-operational/10 text-status-operational';
      case StatusCarregamentoEnum.CARREGANDO:
        return 'bg-status-maintenance/10 text-status-maintenance';
      case StatusCarregamentoEnum.ATRASADO:
        return 'bg-status-warning/10 text-status-warning';
      case StatusCarregamentoEnum.NO_PRAZO:
        return 'bg-status-operational/10 text-status-operational';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[1200px] h-[85vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <span className="text-xl">Operação #{operation.chave}</span>
            <Badge className={getStatusColor(operation.statusFinalCarregamento)}>
              {operation.statusFinalCarregamento || 'Agendado'}
            </Badge>
          </DialogTitle>
          <DialogDescription>
            Cliente: {operation.clientePagador} | {operation.idB100SvnDt}
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="geral" className="w-full flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-6 flex-shrink-0">
            <TabsTrigger value="geral">Geral</TabsTrigger>
            <TabsTrigger value="carregamento">Carregamento</TabsTrigger>
            <TabsTrigger value="descarga">Descarga</TabsTrigger>
            <TabsTrigger value="financeiro">Financeiro</TabsTrigger>
            <TabsTrigger value="documentos">Documentos</TabsTrigger>
            <TabsTrigger value="observacoes">Observações</TabsTrigger>
          </TabsList>

          <TabsContent value="geral" className="flex-1 overflow-y-auto">
            <div className="space-y-6 pr-2">
            {/* Informações Básicas */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Info className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Informações Gerais</span>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between border-b pb-2">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">Motorista</span>
                    </div>
                    <span className="text-sm font-medium">{operation.motorista || '-'}</span>
                  </div>

                  <div className="flex items-center justify-between border-b pb-2">
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">Cliente</span>
                    </div>
                    <span className="text-sm font-medium">{operation.clientePagador}</span>
                  </div>

                  <div className="flex items-center justify-between border-b pb-2">
                    <div className="flex items-center gap-2">
                      <Truck className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">Equipamento</span>
                    </div>
                    <span className="text-sm font-medium">{operation.modeloEquipamento || '-'}</span>
                  </div>

                  <div className="flex items-center justify-between border-b pb-2">
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">Modalidade</span>
                    </div>
                    <span className="text-sm font-medium">{operation.modalidade || '-'}</span>
                  </div>

                  <div className="flex items-center justify-between border-b pb-2">
                    <span className="text-sm">Programador</span>
                    <span className="text-sm font-medium">{operation.programador || '-'}</span>
                  </div>

                  <div className="flex items-center justify-between border-b pb-2">
                    <span className="text-sm">Placa/Tração</span>
                    <span className="text-sm font-medium">{operation.placaTracao || '-'}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Rota e Produto</span>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between border-b pb-2">
                    <span className="text-sm">Origem</span>
                    <span className="text-sm font-medium">{operation.clienteCidadeOrigem || '-'}/{operation.ufOrigem || '-'}</span>
                  </div>

                  <div className="flex items-center justify-between border-b pb-2">
                    <span className="text-sm">Destino</span>
                    <span className="text-sm font-medium">{operation.clienteCidadeDestino || '-'}/{operation.ufDestino || '-'}</span>
                  </div>

                  <div className="flex items-center justify-between border-b pb-2">
                    <span className="text-sm">Peso</span>
                    <span className="text-sm font-medium">{operation.peso || '-'}</span>
                  </div>

                  <div className="flex items-center justify-between border-b pb-2">
                    <span className="text-sm">Dias em Rota</span>
                    <span className="text-sm font-medium">{operation.diasEmRota || 0} dias</span>
                  </div>

                  <div className="flex items-center justify-between border-b pb-2">
                    <span className="text-sm">Nº Rota</span>
                    <span className="text-sm font-medium">{operation.numeroRota || '-'}</span>
                  </div>

                  <div className="flex items-center justify-between border-b pb-2">
                    <span className="text-sm">Código da Operação</span>
                    <span className="text-sm font-medium">{operation.codigoDaOperacao || '-'}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Produto */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Package className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Produto</span>
              </div>
              <div className="p-3 bg-muted/20 rounded-md">
                <span className="text-sm font-medium">{operation.produto || '-'}</span>
              </div>
            </div>

            {/* Agendamento */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Agendamento</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-3 bg-muted/20 rounded-md">
                  <div className="text-xs text-muted-foreground mb-1">Carregamento</div>
                  <div className="text-sm font-medium">{formatDateTime(operation.dataHoraAgendadaCarregamento)}</div>
                </div>
                <div className="p-3 bg-muted/20 rounded-md">
                  <div className="text-xs text-muted-foreground mb-1">Descarga</div>
                  <div className="text-sm font-medium">{formatDateTime(operation.dataHoraAgendadaDescarga)}</div>
                </div>
              </div>
            </div>

            {/* Status da Operação */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Info className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Status da Operação</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Operação Cadastrada?</span>
                  <span className="text-sm font-medium">
                    {operation.operacaoCadastrada ? (
                      <CheckCircle className="h-4 w-4 text-status-operational" />
                    ) : (
                      <XCircle className="h-4 w-4 text-status-warning" />
                    )}
                  </span>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Nº Solicitação de Carga</span>
                  <span className="text-sm font-medium">{operation.numeroSolicitacaoCarga || '-'}</span>
                </div>
              </div>
            </div>
            </div>
          </TabsContent>

          <TabsContent value="carregamento" className="flex-1 overflow-y-auto">
            <div className="space-y-6 pr-2">
            {/* App Cliente e Horários */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Informações de Carregamento</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">App Cliente - Carga</span>
                  <span className="text-sm font-medium">{operation.appClienteCarga || '-'}</span>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Status Final do Carregamento</span>
                  <span className={`text-sm font-medium px-2 py-1 rounded ${getStatusColor(operation.statusFinalCarregamento)}`}>
                    {operation.statusFinalCarregamento || '-'}
                  </span>
                </div>
              </div>
            </div>

            {/* Horários de Carregamento */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Horários</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-3 bg-muted/20 rounded-md">
                  <div className="text-xs text-muted-foreground mb-1">Data/Hora Chegada Carga</div>
                  <div className="text-sm font-medium">{formatDateTime(operation.dataHoraChegadaCarga)}</div>
                </div>
                <div className="p-3 bg-muted/20 rounded-md">
                  <div className="text-xs text-muted-foreground mb-1">Data/Hora Início de Carga</div>
                  <div className="text-sm font-medium">{formatDateTime(operation.dataHoraInicioCarga)}</div>
                </div>
                <div className="p-3 bg-muted/20 rounded-md">
                  <div className="text-xs text-muted-foreground mb-1">Data/Hora Fim de Carregamento</div>
                  <div className="text-sm font-medium">{formatDateTime(operation.dataHoraFimCarregamento)}</div>
                </div>
              </div>
            </div>

            {/* Performance e Tempo */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Info className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Performance e Tempo</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Tempo em Carga</span>
                  <span className="text-sm font-medium">{operation.tempoEmCarga || '-'}</span>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Performance de Carregamento</span>
                  <span className="text-sm font-medium">{operation.performanceCarregamento || '-'}</span>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Causa do Atraso</span>
                  <span className="text-sm font-medium">{operation.causaAtrazoCarregamento || '-'}</span>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Diárias C</span>
                  <span className="text-sm font-medium">{operation.diariasC || '-'}</span>
                </div>
              </div>
            </div>

            {/* Ocorrências */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Ocorrências</span>
              </div>

              <div className="p-3 bg-muted/20 rounded-md">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Nº Ocorrência de Carga</span>
                  <span className="text-sm font-medium">{operation.numeroOcorrenciaCarga || '-'}</span>
                </div>
              </div>
            </div>
            </div>
          </TabsContent>

          <TabsContent value="descarga" className="flex-1 overflow-y-auto">
            <div className="space-y-6 pr-2">
            {/* App Cliente e Status */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Truck className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Informações de Descarga</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">App Cliente - Descarga</span>
                  <span className="text-sm font-medium">{operation.appClienteDescarga || '-'}</span>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Status Final</span>
                  <span className="text-sm font-medium">{operation.statusFinal || '-'}</span>
                </div>
              </div>
            </div>

            {/* Horários de Descarga */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Horários</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-3 bg-muted/20 rounded-md">
                  <div className="text-xs text-muted-foreground mb-1">Data/Hora Chegada P/Descarga</div>
                  <div className="text-sm font-medium">{formatDateTime(operation.dataHoraChegadaDescarga)}</div>
                </div>
                <div className="p-3 bg-muted/20 rounded-md">
                  <div className="text-xs text-muted-foreground mb-1">Data/Hora Início de Descarga</div>
                  <div className="text-sm font-medium">{formatDateTime(operation.dataHoraInicioDescarga)}</div>
                </div>
                <div className="p-3 bg-muted/20 rounded-md">
                  <div className="text-xs text-muted-foreground mb-1">Data/Hora Fim de Descarga</div>
                  <div className="text-sm font-medium">{formatDateTime(operation.dataHoraFimDescarga)}</div>
                </div>
              </div>
            </div>

            {/* Performance e Tempo */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Info className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Performance e Tempo</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Tempo de Descarga</span>
                  <span className="text-sm font-medium">{operation.tempoDescarga || '-'}</span>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Performance de Descarga</span>
                  <span className="text-sm font-medium">{operation.performanceDescarga || '-'}</span>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Causa do Atraso</span>
                  <span className="text-sm font-medium">{operation.causaAtrazoDescarga || '-'}</span>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Diárias D</span>
                  <span className="text-sm font-medium">{operation.diariasD || '-'}</span>
                </div>
              </div>
            </div>

            {/* Status e Ocorrências */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Status e Ocorrências</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Houve Descarga?</span>
                  <span className="text-sm font-medium">
                    {operation.houveDescarga !== undefined ? (
                      operation.houveDescarga ? (
                        <CheckCircle className="h-4 w-4 text-status-operational" />
                      ) : (
                        <XCircle className="h-4 w-4 text-status-warning" />
                      )
                    ) : '-'}
                  </span>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Houve Avaria?</span>
                  <span className="text-sm font-medium">
                    {operation.houveAvaria !== undefined ? (
                      operation.houveAvaria ? (
                        <XCircle className="h-4 w-4 text-status-warning" />
                      ) : (
                        <CheckCircle className="h-4 w-4 text-status-operational" />
                      )
                    ) : '-'}
                  </span>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Houve Devolução?</span>
                  <span className="text-sm font-medium">
                    {operation.houveDevolucao !== undefined ? (
                      operation.houveDevolucao ? (
                        <XCircle className="h-4 w-4 text-status-warning" />
                      ) : (
                        <CheckCircle className="h-4 w-4 text-status-operational" />
                      )
                    ) : '-'}
                  </span>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Nº Ocorrência Descarga</span>
                  <span className="text-sm font-medium">{operation.numeroOcorrenciaDescarga || '-'}</span>
                </div>
              </div>
            </div>

            {/* Devolução e Comprovantes */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Devolução e Comprovantes</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Devolução de Pallet</span>
                  <span className="text-sm font-medium">{operation.devolucaoPallet || '-'}</span>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <span className="text-sm">Comprovante de Descarga</span>
                  <span className="text-sm font-medium">{operation.comprovanteDescarga || '-'}</span>
                </div>
              </div>
            </div>
            </div>
          </TabsContent>

          <TabsContent value="financeiro" className="flex-1 overflow-y-auto">
            <div className="space-y-6 pr-2">
            {/* Valores de Frete */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Valores de Frete</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 bg-muted/20 rounded-md">
                  <div className="text-xs text-muted-foreground mb-1">Frete Empresa NET</div>
                  <div className="text-lg font-medium">{operation.freteEmpresaNet || '-'}</div>
                </div>
                <div className="p-4 bg-muted/20 rounded-md">
                  <div className="text-xs text-muted-foreground mb-1">Frete Terceiro</div>
                  <div className="text-lg font-medium">{operation.freteTerceiro || '-'}</div>
                </div>
                <div className="p-4 bg-muted/20 rounded-md">
                  <div className="text-xs text-muted-foreground mb-1">Frete Agregado</div>
                  <div className="text-lg font-medium">{operation.freteAgregado || '-'}</div>
                </div>
              </div>
            </div>

            {/* Margens */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Receipt className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Margens</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-muted/20 rounded-md">
                  <div className="text-xs text-muted-foreground mb-1">Margem Terceiro</div>
                  <div className="text-lg font-medium">{operation.margemTerceiro || '-'}</div>
                </div>
                <div className="p-4 bg-muted/20 rounded-md">
                  <div className="text-xs text-muted-foreground mb-1">Margem Agregado</div>
                  <div className="text-lg font-medium">{operation.margemAgregado || '-'}</div>
                </div>
              </div>
            </div>

            {/* Resumo Financeiro */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Info className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Resumo por Modalidade</span>
              </div>

              <div className="p-4 border rounded-md">
                <div className="text-sm font-medium mb-2">Modalidade: {operation.modalidade}</div>
                <div className="text-xs text-muted-foreground">
                  {operation.modalidade === 'Frota' && 'Valores de frete da empresa própria'}
                  {operation.modalidade === 'Terceiro' && 'Valores de frete para terceiros com margem aplicada'}
                  {operation.modalidade === 'Agregado' && 'Valores de frete para agregados com margem aplicada'}
                </div>
              </div>
            </div>
            </div>
          </TabsContent>

          <TabsContent value="documentos" className="flex-1 overflow-y-auto">
            <div className="space-y-6 pr-2">
            {/* Documentos Fiscais */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Documentos Fiscais</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-muted/20 rounded-md">
                  <div className="text-xs text-muted-foreground mb-1">Número Nota Fiscal</div>
                  <div className="text-sm font-medium">{operation.numeroNotaFiscal || '-'}</div>
                </div>
                <div className="p-4 bg-muted/20 rounded-md">
                  <div className="text-xs text-muted-foreground mb-1">Número CTE</div>
                  <div className="text-sm font-medium">{operation.numeroCTE || '-'}</div>
                </div>
              </div>
            </div>

            {/* Status dos Documentos */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Info className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Status dos Documentos</span>
              </div>

              <div className="grid grid-cols-1 gap-4">
                <div className="p-4 border rounded-md">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Nota Fiscal</span>
                    <span className="text-xs px-2 py-1 rounded bg-status-operational/10 text-status-operational">
                      {operation.numeroNotaFiscal ? 'Emitida' : 'Pendente'}
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {operation.numeroNotaFiscal ?
                      `Documento fiscal emitido: ${operation.numeroNotaFiscal}` :
                      'Aguardando emissão da nota fiscal'
                    }
                  </div>
                </div>

                <div className="p-4 border rounded-md">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">CTE (Conhecimento de Transporte Eletrônico)</span>
                    <span className={`text-xs px-2 py-1 rounded ${
                      operation.numeroCTE ?
                        'bg-status-operational/10 text-status-operational' :
                        'bg-status-warning/10 text-status-warning'
                    }`}>
                      {operation.numeroCTE ? 'Emitido' : 'Sem CTE'}
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {operation.numeroCTE ?
                      `Autorização de transporte emitida: ${operation.numeroCTE}` :
                      'ATENÇÃO: Motorista não pode sair sem CTE!'
                    }
                  </div>
                </div>
              </div>
            </div>

            {/* Identificação do Cliente */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Building className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Identificação do Cliente</span>
              </div>

              <div className="p-4 bg-muted/20 rounded-md">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="text-xs text-muted-foreground mb-1">Cliente-Pagador</div>
                    <div className="text-sm font-medium">{operation.clientePagador}</div>
                  </div>
                  <div>
                    <div className="text-xs text-muted-foreground mb-1">ID/B100/SVN/Nº DT</div>
                    <div className="text-sm font-medium">{operation.idB100SvnDt || '-'}</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Alertas Importantes */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-status-warning" />
                <span className="text-sm font-medium text-status-warning">Alertas Importantes</span>
              </div>

              <div className="p-4 border border-status-warning/30 bg-status-warning/5 rounded-md">
                <div className="text-sm font-medium mb-2">Documentação Obrigatória</div>
                <ul className="text-xs text-muted-foreground space-y-1">
                  <li>• CTE é obrigatório para transporte de cargas</li>
                  <li>• Motorista não deve sair sem aguardar a emissão do CTE</li>
                  <li>• Nota Fiscal deve acompanhar a mercadoria</li>
                  <li>• Verificar se todos os documentos estão corretos antes da saída</li>
                </ul>
              </div>
            </div>
            </div>
          </TabsContent>
          <TabsContent value="observacoes" className="flex-1 overflow-y-auto">
            <div className="space-y-6 pr-2">
            {/* Observações Gerais */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Info className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Observações</span>
              </div>

              <div className="p-4 bg-muted/20 rounded-md min-h-[100px]">
                <div className="text-sm">
                  {operation.observacao || 'Nenhuma observação registrada para esta operação.'}
                </div>
              </div>
            </div>

            {/* Resumo da Operação */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Resumo da Operação</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-md">
                  <div className="text-sm font-medium mb-2">Status Atual</div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-xs">
                      <span>Operação Cadastrada:</span>
                      <span>{operation.operacaoCadastrada ? '✓ Sim' : '✗ Não'}</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span>Status Carregamento:</span>
                      <span>{operation.statusFinalCarregamento || 'Agendado'}</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span>Status Descarga:</span>
                      <span>{operation.statusFinal || 'Pendente'}</span>
                    </div>
                  </div>
                </div>

                <div className="p-4 border rounded-md">
                  <div className="text-sm font-medium mb-2">Documentação</div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-xs">
                      <span>Nota Fiscal:</span>
                      <span>{operation.numeroNotaFiscal ? '✓ Emitida' : '⏳ Pendente'}</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span>CTE:</span>
                      <span className={operation.numeroCTE ? 'text-status-operational' : 'text-status-warning'}>
                        {operation.numeroCTE ? '✓ Emitido' : '⚠️ Sem CTE'}
                      </span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span>Houve Descarga:</span>
                      <span>
                        {operation.houveDescarga === undefined ? '-' :
                         operation.houveDescarga ? '✓ Sim' : '✗ Não'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Histórico de Alterações */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Informações Adicionais</span>
              </div>

              <div className="p-4 bg-muted/20 rounded-md">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
                  <div>
                    <span className="font-medium">Programador:</span> {operation.programador || '-'}
                  </div>
                  <div>
                    <span className="font-medium">Placa/Tração:</span> {operation.placaTracao || '-'}
                  </div>
                  <div>
                    <span className="font-medium">Código da Operação:</span> {operation.codigoDaOperacao || '-'}
                  </div>
                  <div>
                    <span className="font-medium">Nº Solicitação de Carga:</span> {operation.numeroSolicitacaoCarga || '-'}
                  </div>
                </div>
              </div>
            </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="gap-2 mt-4 flex-shrink-0 border-t pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Fechar
          </Button>
          <Button onClick={onEdit}>
            Editar Operação
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default OperationDetails;


import React, { useState } from 'react';
import Navbar from '@/components/layout/Navbar';
import Sidebar from '@/components/layout/Sidebar';
import { Button } from "@/components/ui/button";
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Calendar, Clock, Filter, Plus, Search, Truck, User, FileText, MapPin } from 'lucide-react';
import { Operation, ClienteEnum, ModalidadeEnum, StatusCarregamentoEnum, StatusDescargaEnum } from '@/types';
import { useToast } from '@/hooks/use-toast';
import OperationDialog from '@/components/operations/OperationDialog';
import OperationDetails from '@/components/operations/OperationDetails';

// Mock data for operations baseado na planilha
const initialOperations: Operation[] = [
  {
    chave: "413",
    dataHoraAgendadaCarregamento: "05/06/2025 07:00:00",
    dataHoraAgendadaDescarga: "09/06/2025 07:00:00",
    produto: "PRODUTOS ALIMENTICIOS",
    clientePagador: ClienteEnum.PEPSICO,
    idB100SvnDt: "DT 11763733",
    clienteCidadeOrigem: "Cabreúva",
    ufOrigem: "SP",
    clienteCidadeDestino: "Anápolis",
    ufDestino: "GO",
    peso: "16 TON",
    motorista: "RODRIGO CALDAS",
    modalidade: ModalidadeEnum.FROTA,
    diasEmRota: 4,
    modeloEquipamento: "Truck",
    operacaoCadastrada: true,
    programador: "Paulo/Gabriel"
  },
  {
    chave: "426",
    dataHoraAgendadaCarregamento: "04/06/2025 16:00:00",
    dataHoraAgendadaDescarga: "06/06/2025 22:00:00",
    produto: "PRODUTOS ALIMENTICIOS BITREM",
    clientePagador: ClienteEnum.PEPSICO,
    idB100SvnDt: "DT 11786573",
    clienteCidadeOrigem: "Sete Lagoas",
    ufOrigem: "MG",
    clienteCidadeDestino: "Cabreúva",
    ufDestino: "SP",
    peso: "4 TON",
    motorista: "PEDRO GALVAO LEMOS",
    modalidade: ModalidadeEnum.FROTA,
    diasEmRota: 10,
    modeloEquipamento: "Bitrem",
    operacaoCadastrada: true,
    codigoDaOperacao: "308622880",
    freteEmpresaNet: "R$ 5.000,00",
    numeroSolicitacaoCarga: "139563",
    placaTracao: "RCH5D99",
    programador: "Paulo/Gabriel",
    dataHoraChegadaCarga: "04/06/2025 13:24:00",
    dataHoraInicioCarga: "04/06/2025 13:24:00",
    statusFinalCarregamento: StatusCarregamentoEnum.CARREGANDO,
    performanceCarregamento: "NO PRAZO",
    causaAtrazoCarregamento: "Não houve atrazo"
  },
  {
    chave: "427",
    dataHoraAgendadaCarregamento: "04/06/2025 16:00:00",
    dataHoraAgendadaDescarga: "06/06/2025 22:00:00",
    produto: "PRODUTOS ALIMENTICIOS BITREM",
    clientePagador: ClienteEnum.PEPSICO,
    idB100SvnDt: "DT 11786572",
    clienteCidadeOrigem: "Sete Lagoas",
    ufOrigem: "MG",
    clienteCidadeDestino: "Cabreúva",
    ufDestino: "SP",
    peso: "4 TON",
    motorista: "PEDRO GALVAO LEMOS",
    modalidade: ModalidadeEnum.FROTA,
    diasEmRota: 10,
    modeloEquipamento: "Bitrem",
    operacaoCadastrada: true,
    codigoDaOperacao: "308622880",
    freteEmpresaNet: "R$ 5.000,00",
    numeroSolicitacaoCarga: "139562",
    placaTracao: "RCH5D99",
    programador: "Paulo/Gabriel",
    dataHoraChegadaCarga: "04/06/2025 13:24:00",
    dataHoraInicioCarga: "04/06/2025 13:24:00",
    statusFinalCarregamento: StatusCarregamentoEnum.CARREGANDO,
    performanceCarregamento: "NO PRAZO",
    causaAtrazoCarregamento: "Não houve atrazo"
  }
];

// Mock data for available operators and equipment
const availableOperators = [
  { id: 'OP001', name: 'RODRIGO CALDAS' },
  { id: 'OP002', name: 'PEDRO GALVAO LEMOS' },
  { id: 'OP003', name: 'Carlos Silva' },
  { id: 'OP004', name: 'Maria Oliveira' },
  { id: 'OP005', name: 'João Pereira' }
];

const availableEquipments = [
  { id: 'EQ001', model: 'Truck' },
  { id: 'EQ002', model: 'Bitrem' },
  { id: 'EQ003', model: 'Carreta' },
  { id: 'EQ004', model: 'Toco' }
];

const availableProgrammers = [
  'Paulo/Gabriel',
  'Ana/Carlos',
  'Maria/João'
];

const OperationsPage = () => {
  const isMobile = useIsMobile();
  const { toast } = useToast();
  const [search, setSearch] = useState('');
  const [cliente, setCliente] = useState<string>('all');
  const [modalidade, setModalidade] = useState<string>('all');
  const [operations, setOperations] = useState<Operation[]>(initialOperations);
  
  // Dialog states
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [selectedOperation, setSelectedOperation] = useState<Operation | null>(null);
  
  // Filter operations based on search and filters
  const filteredOperations = operations.filter(operation => {
    // Search filter
    const matchesSearch = operation.motorista.toLowerCase().includes(search.toLowerCase()) ||
                          operation.chave.toLowerCase().includes(search.toLowerCase()) ||
                          operation.produto.toLowerCase().includes(search.toLowerCase()) ||
                          operation.clienteCidadeOrigem.toLowerCase().includes(search.toLowerCase()) ||
                          operation.clienteCidadeDestino.toLowerCase().includes(search.toLowerCase());

    // Cliente filter
    const matchesCliente = cliente === 'all' || operation.clientePagador === cliente;

    // Modalidade filter
    const matchesModalidade = modalidade === 'all' || operation.modalidade === modalidade;

    return matchesSearch && matchesCliente && matchesModalidade;
  });

  // Format date/time from Brazilian format
  const formatDateTime = (dateTimeString?: string) => {
    if (!dateTimeString) return '-';
    return dateTimeString;
  };

  // Get status color for carregamento
  const getStatusColor = (status?: StatusCarregamentoEnum) => {
    switch (status) {
      case StatusCarregamentoEnum.CARREGADO:
        return 'bg-status-operational/10 text-status-operational';
      case StatusCarregamentoEnum.CARREGANDO:
        return 'bg-status-maintenance/10 text-status-maintenance';
      case StatusCarregamentoEnum.ATRASADO:
        return 'bg-status-warning/10 text-status-warning';
      case StatusCarregamentoEnum.NO_PRAZO:
        return 'bg-status-operational/10 text-status-operational';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  // Handle save operation
  const handleSaveOperation = (operationData: Operation) => {
    const isNewOperation = !operations.some(op => op.chave === operationData.chave);

    if (isNewOperation) {
      // Add new operation
      setOperations(prev => [operationData, ...prev]);
      toast({
        title: "Operação criada",
        description: "A operação foi criada com sucesso."
      });
    } else {
      // Update existing operation
      setOperations(prev =>
        prev.map(op => op.chave === operationData.chave ? operationData : op)
      );
      toast({
        title: "Operação atualizada",
        description: "A operação foi atualizada com sucesso."
      });
    }
  };

  // Open details dialog
  const handleViewDetails = (operation: Operation) => {
    setSelectedOperation(operation);
    setDetailsDialogOpen(true);
  };

  // Open edit dialog from details
  const handleEditFromDetails = () => {
    setDetailsDialogOpen(false);
    setEditDialogOpen(true);
  };

  return (
    <div className="flex min-h-screen bg-background">
      <Sidebar />
      
      <div className={cn(
        "flex-1 flex flex-col",
        !isMobile && "ml-64" // Offset for sidebar when not mobile
      )}>
        <Navbar 
          title="Operações" 
          subtitle="Controle de Operações"
        />
        
        <main className="flex-1 px-6 py-6">
          {/* Filter section */}
          <div className="flex flex-col sm:flex-row justify-between gap-4 mb-6">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input 
                type="text" 
                placeholder="Buscar operação..." 
                className="pl-10"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <div className="relative">
                <Button variant="outline" className="flex items-center gap-2">
                  <Filter className="w-4 h-4" />
                  Filtrar
                </Button>
              </div>
              <Button 
                className="gap-2"
                onClick={() => {
                  setSelectedOperation(null);
                  setAddDialogOpen(true);
                }}
              >
                <Plus className="w-4 h-4" />
                Nova Operação
              </Button>
            </div>
          </div>
          
          {/* Filter options */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Cliente</h4>
              <select
                className="w-full p-2 rounded-md border border-input bg-background"
                value={cliente}
                onChange={(e) => setCliente(e.target.value)}
              >
                <option value="all">Todos os Clientes</option>
                <option value={ClienteEnum.NOVELIS}>{ClienteEnum.NOVELIS}</option>
                <option value={ClienteEnum.PEPSICO}>{ClienteEnum.PEPSICO}</option>
                <option value={ClienteEnum.AMBEV}>{ClienteEnum.AMBEV}</option>
              </select>
            </div>
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Modalidade</h4>
              <select
                className="w-full p-2 rounded-md border border-input bg-background"
                value={modalidade}
                onChange={(e) => setModalidade(e.target.value)}
              >
                <option value="all">Todas as Modalidades</option>
                <option value={ModalidadeEnum.FROTA}>{ModalidadeEnum.FROTA}</option>
                <option value={ModalidadeEnum.TERCEIRO}>{ModalidadeEnum.TERCEIRO}</option>
                <option value={ModalidadeEnum.AGREGADO}>{ModalidadeEnum.AGREGADO}</option>
              </select>
            </div>
          </div>
          
          {/* Active Operations */}
          <div className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">Operações em Andamento</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredOperations
                .filter(op => op.statusFinalCarregamento === StatusCarregamentoEnum.CARREGANDO || !op.statusFinalCarregamento)
                .map((operation) => (
                  <div key={operation.chave} className="bg-card border rounded-lg overflow-hidden shadow">
                    <div className="p-4">
                      <div className="flex justify-between items-start mb-3">
                        <div>
                          <h3 className="font-medium">{operation.motorista}</h3>
                          <p className="text-sm text-muted-foreground">Chave: {operation.chave}</p>
                        </div>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs ${getStatusColor(operation.statusFinalCarregamento)}`}>
                          {operation.statusFinalCarregamento || 'Agendado'}
                        </span>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Truck className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm">{operation.modeloEquipamento}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <User className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm">{operation.clientePagador}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm">{operation.clienteCidadeOrigem}/{operation.ufOrigem} → {operation.clienteCidadeDestino}/{operation.ufDestino}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm">Carregamento: {formatDateTime(operation.dataHoraAgendadaCarregamento)}</span>
                        </div>
                      </div>
                    </div>

                    <div className="border-t px-4 py-3 bg-muted/30 flex justify-between">
                      <span className="text-sm">Peso: {operation.peso}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewDetails(operation)}
                      >
                        Detalhes
                      </Button>
                    </div>
                  </div>
                ))}

              {filteredOperations.filter(op => op.statusFinalCarregamento === StatusCarregamentoEnum.CARREGANDO || !op.statusFinalCarregamento).length === 0 && (
                <div className="col-span-full p-8 text-center bg-card border rounded-lg">
                  <p className="text-muted-foreground">Nenhuma operação em andamento</p>
                </div>
              )}
            </div>
          </div>
          
          {/* All Operations Table */}
          <div>
            <h2 className="text-2xl font-semibold mb-4">Todas as Operações</h2>
            <div className="bg-card rounded-lg shadow overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-muted/50">
                    <tr>
                      <th className="p-4 text-left font-medium text-muted-foreground">Chave</th>
                      <th className="p-4 text-left font-medium text-muted-foreground">Cliente</th>
                      <th className="p-4 text-left font-medium text-muted-foreground">Motorista</th>
                      <th className="p-4 text-left font-medium text-muted-foreground">Rota</th>
                      <th className="p-4 text-left font-medium text-muted-foreground">Produto</th>
                      <th className="p-4 text-left font-medium text-muted-foreground">Peso</th>
                      <th className="p-4 text-left font-medium text-muted-foreground">Modalidade</th>
                      <th className="p-4 text-left font-medium text-muted-foreground">Status</th>
                      <th className="p-4 text-left font-medium text-muted-foreground">Ações</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-border">
                    {filteredOperations.map((operation) => (
                      <tr key={operation.chave} className="hover:bg-muted/50 transition-colors">
                        <td className="p-4 font-medium">{operation.chave}</td>
                        <td className="p-4">
                          <div className="font-medium">{operation.clientePagador}</div>
                          <div className="text-xs text-muted-foreground">{operation.idB100SvnDt}</div>
                        </td>
                        <td className="p-4">{operation.motorista}</td>
                        <td className="p-4">
                          <div className="text-sm">{operation.clienteCidadeOrigem}/{operation.ufOrigem}</div>
                          <div className="text-xs text-muted-foreground">→ {operation.clienteCidadeDestino}/{operation.ufDestino}</div>
                        </td>
                        <td className="p-4">
                          <div className="text-sm">{operation.produto}</div>
                          <div className="text-xs text-muted-foreground">{operation.modeloEquipamento}</div>
                        </td>
                        <td className="p-4">{operation.peso}</td>
                        <td className="p-4">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary/10 text-primary">
                            {operation.modalidade}
                          </span>
                        </td>
                        <td className="p-4">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${getStatusColor(operation.statusFinalCarregamento)}`}>
                            {operation.statusFinalCarregamento || 'Agendado'}
                          </span>
                        </td>
                        <td className="p-4">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewDetails(operation)}
                          >
                            Detalhes
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {filteredOperations.length === 0 && (
                <div className="p-8 text-center">
                  <p className="text-muted-foreground">Nenhuma operação encontrada</p>
                </div>
              )}
            </div>
          </div>
        </main>
      </div>

      {/* Add Operation Dialog */}
      <OperationDialog
        open={addDialogOpen}
        onOpenChange={setAddDialogOpen}
        onSave={handleSaveOperation}
        availableOperators={availableOperators}
        availableEquipments={availableEquipments}
        availableProgrammers={availableProgrammers}
      />

      {/* Edit Operation Dialog */}
      <OperationDialog
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        operation={selectedOperation || undefined}
        onSave={handleSaveOperation}
        availableOperators={availableOperators}
        availableEquipments={availableEquipments}
        availableProgrammers={availableProgrammers}
      />

      {/* Operation Details Dialog */}
      <OperationDetails
        open={detailsDialogOpen}
        onOpenChange={setDetailsDialogOpen}
        operation={selectedOperation}
        onEdit={handleEditFromDetails}
      />
    </div>
  );
};

export default OperationsPage;

import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Operation, ClienteEnum, ModalidadeEnum, StatusCarregamentoEnum, StatusDescargaEnum } from '@/types';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from '@/hooks/use-toast';

interface OperationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  operation?: Operation;
  onSave: (operation: Operation) => void;
  availableOperators: { id: string; name: string }[];
  availableEquipments: { id: string; model: string }[];
  availableProgrammers: string[];
}

const OperationDialog = ({
  open,
  onOpenChange,
  operation,
  onSave,
  availableOperators,
  availableEquipments,
  availableProgrammers
}: OperationDialogProps) => {
  const { toast } = useToast();
  const isEditing = !!operation;

  // Form state - Initialize with all Operation fields
  const [formData, setFormData] = useState<Partial<Operation>>({
    // Identificação
    chave: '',
    dataHoraAgendadaCarregamento: '',
    dataHoraAgendadaDescarga: '',
    produto: '',
    clientePagador: ClienteEnum.PEPSICO,
    idB100SvnDt: '',
    
    // Origem e Destino
    clienteCidadeOrigem: '',
    ufOrigem: '',
    clienteCidadeDestino: '',
    ufDestino: '',
    peso: '',
    
    // Motorista e Equipamento
    motorista: '',
    modalidade: ModalidadeEnum.FROTA,
    diasEmRota: 0,
    modeloEquipamento: '',
    
    // Operação
    operacaoCadastrada: true,
    codigoDaOperacao: '',
    numeroRota: '',
    
    // Valores Financeiros
    freteEmpresaNet: '',
    freteTerceiro: '',
    margemTerceiro: '',
    freteAgregado: '',
    margemAgregado: '',
    
    // Logística
    numeroSolicitacaoCarga: '',
    placaTracao: '',
    programador: '',
    
    // Carregamento
    appClienteCarga: '',
    dataHoraChegadaCarga: '',
    dataHoraInicioCarga: '',
    dataHoraFimCarregamento: '',
    tempoEmCarga: '',
    statusFinalCarregamento: undefined,
    diariasC: '',
    performanceCarregamento: '',
    causaAtrazoCarregamento: '',
    
    // Documentação
    numeroNotaFiscal: '',
    numeroCTE: '',
    numeroOcorrenciaCarga: '',
    
    // Descarga
    appClienteDescarga: '',
    dataHoraChegadaDescarga: '',
    dataHoraInicioDescarga: '',
    dataHoraFimDescarga: '',
    tempoDescarga: '',
    statusFinal: undefined,
    performanceDescarga: '',
    causaAtrazoDescarga: '',
    diariasD: '',
    numeroOcorrenciaDescarga: '',
    
    // Finalização
    houveDescarga: undefined,
    devolucaoPallet: '',
    houveAvaria: undefined,
    houveDevolucao: undefined,
    comprovanteDescarga: '',
    observacao: ''
  });

  // Initialize form with operation data if editing
  useEffect(() => {
    if (operation) {
      setFormData(operation);
    } else {
      // Reset form for new operation with all fields
      setFormData({
        // Identificação
        chave: '',
        dataHoraAgendadaCarregamento: '',
        dataHoraAgendadaDescarga: '',
        produto: '',
        clientePagador: ClienteEnum.PEPSICO,
        idB100SvnDt: '',
        
        // Origem e Destino
        clienteCidadeOrigem: '',
        ufOrigem: '',
        clienteCidadeDestino: '',
        ufDestino: '',
        peso: '',
        
        // Motorista e Equipamento
        motorista: '',
        modalidade: ModalidadeEnum.FROTA,
        diasEmRota: 0,
        modeloEquipamento: '',
        
        // Operação
        operacaoCadastrada: true,
        codigoDaOperacao: '',
        numeroRota: '',
        
        // Valores Financeiros
        freteEmpresaNet: '',
        freteTerceiro: '',
        margemTerceiro: '',
        freteAgregado: '',
        margemAgregado: '',
        
        // Logística
        numeroSolicitacaoCarga: '',
        placaTracao: '',
        programador: '',
        
        // Carregamento
        appClienteCarga: '',
        dataHoraChegadaCarga: '',
        dataHoraInicioCarga: '',
        dataHoraFimCarregamento: '',
        tempoEmCarga: '',
        statusFinalCarregamento: undefined,
        diariasC: '',
        performanceCarregamento: '',
        causaAtrazoCarregamento: '',
        
        // Documentação
        numeroNotaFiscal: '',
        numeroCTE: '',
        numeroOcorrenciaCarga: '',
        
        // Descarga
        appClienteDescarga: '',
        dataHoraChegadaDescarga: '',
        dataHoraInicioDescarga: '',
        dataHoraFimDescarga: '',
        tempoDescarga: '',
        statusFinal: undefined,
        performanceDescarga: '',
        causaAtrazoDescarga: '',
        diariasD: '',
        numeroOcorrenciaDescarga: '',
        
        // Finalização
        houveDescarga: undefined,
        devolucaoPallet: '',
        houveAvaria: undefined,
        houveDevolucao: undefined,
        comprovanteDescarga: '',
        observacao: ''
      });
    }
  }, [operation, open]);

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;

    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? Number(value) : value
    }));
  };

  // Handle operator selection
  const handleOperatorChange = (motorista: string) => {
    setFormData(prev => ({
      ...prev,
      motorista
    }));
  };

  // Handle equipment selection
  const handleEquipmentChange = (modeloEquipamento: string) => {
    setFormData(prev => ({
      ...prev,
      modeloEquipamento
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!formData.chave || !formData.motorista || !formData.clientePagador || !formData.produto) {
      toast({
        title: "Erro de validação",
        description: "Por favor, preencha todos os campos obrigatórios.",
        variant: "destructive"
      });
      return;
    }

    // Generate chave for new operations if not provided
    const operationData: Operation = {
      chave: operation?.chave || `${Math.floor(Math.random() * 1000)}`,
      ...formData as Operation
    };

    // Save the operation
    onSave(operationData);

    // Close the dialog
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[1200px] h-[85vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>
            {isEditing ? 'Editar Operação' : 'Nova Operação'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="flex-1 flex flex-col">
          <Tabs defaultValue="geral" className="w-full flex-1 flex flex-col">
            <TabsList className="grid w-full grid-cols-6 flex-shrink-0">
              <TabsTrigger value="geral">Geral</TabsTrigger>
              <TabsTrigger value="carregamento">Carregamento</TabsTrigger>
              <TabsTrigger value="descarga">Descarga</TabsTrigger>
              <TabsTrigger value="financeiro">Financeiro</TabsTrigger>
              <TabsTrigger value="documentos">Documentos</TabsTrigger>
              <TabsTrigger value="observacoes">Observações</TabsTrigger>
            </TabsList>

            {/* Aba Geral */}
            <TabsContent value="geral" className="flex-1 overflow-hidden">
              <ScrollArea className="h-full pr-4">
                <div className="space-y-6 pb-4">
                  {/* Identificação */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Identificação</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="chave">Chave *</Label>
                        <Input
                          id="chave"
                          name="chave"
                          value={formData.chave}
                          onChange={handleChange}
                          placeholder="Ex: 413"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="clientePagador">Cliente *</Label>
                        <Select
                          value={formData.clientePagador}
                          onValueChange={(value) => setFormData(prev => ({ ...prev, clientePagador: value as ClienteEnum }))}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o cliente" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value={ClienteEnum.NOVELIS}>{ClienteEnum.NOVELIS}</SelectItem>
                            <SelectItem value={ClienteEnum.PEPSICO}>{ClienteEnum.PEPSICO}</SelectItem>
                            <SelectItem value={ClienteEnum.AMBEV}>{ClienteEnum.AMBEV}</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="idB100SvnDt">ID/B100/SVN/Nº DT</Label>
                        <Input
                          id="idB100SvnDt"
                          name="idB100SvnDt"
                          value={formData.idB100SvnDt}
                          onChange={handleChange}
                          placeholder="Ex: DT 11763733"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Agendamento */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Agendamento</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="dataHoraAgendadaCarregamento">Data/Hora Agendada P/ Carregamento</Label>
                        <Input
                          id="dataHoraAgendadaCarregamento"
                          name="dataHoraAgendadaCarregamento"
                          value={formData.dataHoraAgendadaCarregamento}
                          onChange={handleChange}
                          placeholder="05/06/2025 07:00:00"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="dataHoraAgendadaDescarga">Data/Hora Agendada P/ Descarga</Label>
                        <Input
                          id="dataHoraAgendadaDescarga"
                          name="dataHoraAgendadaDescarga"
                          value={formData.dataHoraAgendadaDescarga}
                          onChange={handleChange}
                          placeholder="09/06/2025 07:00:00"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Produto e Rota */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Produto e Rota</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="produto">Produto *</Label>
                        <Input
                          id="produto"
                          name="produto"
                          value={formData.produto}
                          onChange={handleChange}
                          placeholder="Ex: PRODUTOS ALIMENTICIOS"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="peso">Peso</Label>
                        <Input
                          id="peso"
                          name="peso"
                          value={formData.peso}
                          onChange={handleChange}
                          placeholder="Ex: 16 TON"
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="clienteCidadeOrigem">Cidade Origem</Label>
                        <Input
                          id="clienteCidadeOrigem"
                          name="clienteCidadeOrigem"
                          value={formData.clienteCidadeOrigem}
                          onChange={handleChange}
                          placeholder="Ex: Cabreúva"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="ufOrigem">UF Origem</Label>
                        <Input
                          id="ufOrigem"
                          name="ufOrigem"
                          value={formData.ufOrigem}
                          onChange={handleChange}
                          placeholder="Ex: SP"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="clienteCidadeDestino">Cidade Destino</Label>
                        <Input
                          id="clienteCidadeDestino"
                          name="clienteCidadeDestino"
                          value={formData.clienteCidadeDestino}
                          onChange={handleChange}
                          placeholder="Ex: Anápolis"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="ufDestino">UF Destino</Label>
                        <Input
                          id="ufDestino"
                          name="ufDestino"
                          value={formData.ufDestino}
                          onChange={handleChange}
                          placeholder="Ex: GO"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Motorista e Equipamento */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Motorista e Equipamento</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="motorista">Motorista *</Label>
                        <Select
                          value={formData.motorista}
                          onValueChange={handleOperatorChange}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione um motorista" />
                          </SelectTrigger>
                          <SelectContent>
                            {availableOperators.map(operator => (
                              <SelectItem key={operator.id} value={operator.name}>
                                {operator.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="modeloEquipamento">Modelo/Equipamento</Label>
                        <Select
                          value={formData.modeloEquipamento}
                          onValueChange={handleEquipmentChange}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o equipamento" />
                          </SelectTrigger>
                          <SelectContent>
                            {availableEquipments.map(equipment => (
                              <SelectItem key={equipment.id} value={equipment.model}>
                                {equipment.model}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="modalidade">Modalidade</Label>
                        <Select
                          value={formData.modalidade}
                          onValueChange={(value) => setFormData(prev => ({ ...prev, modalidade: value as ModalidadeEnum }))}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione a modalidade" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value={ModalidadeEnum.FROTA}>{ModalidadeEnum.FROTA}</SelectItem>
                            <SelectItem value={ModalidadeEnum.TERCEIRO}>{ModalidadeEnum.TERCEIRO}</SelectItem>
                            <SelectItem value={ModalidadeEnum.AGREGADO}>{ModalidadeEnum.AGREGADO}</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="diasEmRota">Dias em Rota</Label>
                        <Input
                          id="diasEmRota"
                          name="diasEmRota"
                          type="number"
                          value={formData.diasEmRota}
                          onChange={handleChange}
                          placeholder="Ex: 4"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="programador">Programador</Label>
                        <Select
                          value={formData.programador}
                          onValueChange={(value) => setFormData(prev => ({ ...prev, programador: value }))}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o programador" />
                          </SelectTrigger>
                          <SelectContent>
                            {availableProgrammers.map(programmer => (
                              <SelectItem key={programmer} value={programmer}>
                                {programmer}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="placaTracao">Placa/Tração</Label>
                        <Input
                          id="placaTracao"
                          name="placaTracao"
                          value={formData.placaTracao}
                          onChange={handleChange}
                          placeholder="Ex: RCH5D99"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Operação e Códigos */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Operação e Códigos</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="codigoDaOperacao">Código da Operação</Label>
                        <Input
                          id="codigoDaOperacao"
                          name="codigoDaOperacao"
                          value={formData.codigoDaOperacao}
                          onChange={handleChange}
                          placeholder="Ex: 308622880"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="numeroRota">Nº Rota</Label>
                        <Input
                          id="numeroRota"
                          name="numeroRota"
                          value={formData.numeroRota}
                          onChange={handleChange}
                          placeholder="Número da rota"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="numeroSolicitacaoCarga">Nº Solicitação de Carga</Label>
                        <Input
                          id="numeroSolicitacaoCarga"
                          name="numeroSolicitacaoCarga"
                          value={formData.numeroSolicitacaoCarga}
                          onChange={handleChange}
                          placeholder="Ex: 139563"
                        />
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="operacaoCadastrada"
                        checked={formData.operacaoCadastrada}
                        onCheckedChange={(checked) => setFormData(prev => ({ ...prev, operacaoCadastrada: checked as boolean }))}
                      />
                      <Label htmlFor="operacaoCadastrada">Operação Cadastrada?</Label>
                    </div>
                  </div>
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Aba Carregamento */}
            <TabsContent value="carregamento" className="flex-1 overflow-hidden">
              <ScrollArea className="h-full pr-4">
                <div className="space-y-6 pb-4">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Informações de Carregamento</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="appClienteCarga">App Cliente - Carga</Label>
                        <Input
                          id="appClienteCarga"
                          name="appClienteCarga"
                          value={formData.appClienteCarga}
                          onChange={handleChange}
                          placeholder="Ex: App do cliente"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="statusFinalCarregamento">Status Final do Carregamento</Label>
                        <Select
                          value={formData.statusFinalCarregamento}
                          onValueChange={(value) => setFormData(prev => ({ ...prev, statusFinalCarregamento: value as StatusCarregamentoEnum }))}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value={StatusCarregamentoEnum.CARREGANDO}>{StatusCarregamentoEnum.CARREGANDO}</SelectItem>
                            <SelectItem value={StatusCarregamentoEnum.CARREGADO}>{StatusCarregamentoEnum.CARREGADO}</SelectItem>
                            <SelectItem value={StatusCarregamentoEnum.NO_PRAZO}>{StatusCarregamentoEnum.NO_PRAZO}</SelectItem>
                            <SelectItem value={StatusCarregamentoEnum.ATRASADO}>{StatusCarregamentoEnum.ATRASADO}</SelectItem>
                            <SelectItem value={StatusCarregamentoEnum.CANCELADO}>{StatusCarregamentoEnum.CANCELADO}</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Horários de Carregamento</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="dataHoraChegadaCarga">Data/Hora Chegada Carga</Label>
                        <Input
                          id="dataHoraChegadaCarga"
                          name="dataHoraChegadaCarga"
                          value={formData.dataHoraChegadaCarga}
                          onChange={handleChange}
                          placeholder="05/06/2025 06:30:00"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="dataHoraInicioCarga">Data/Hora Início de Carga</Label>
                        <Input
                          id="dataHoraInicioCarga"
                          name="dataHoraInicioCarga"
                          value={formData.dataHoraInicioCarga}
                          onChange={handleChange}
                          placeholder="05/06/2025 07:00:00"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="dataHoraFimCarregamento">Data/Hora Fim de Carregamento</Label>
                        <Input
                          id="dataHoraFimCarregamento"
                          name="dataHoraFimCarregamento"
                          value={formData.dataHoraFimCarregamento}
                          onChange={handleChange}
                          placeholder="05/06/2025 09:30:00"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Performance e Controle</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="tempoEmCarga">Tempo em Carga</Label>
                        <Input
                          id="tempoEmCarga"
                          name="tempoEmCarga"
                          value={formData.tempoEmCarga}
                          onChange={handleChange}
                          placeholder="Ex: 2h 30min"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="performanceCarregamento">Performance de Carregamento</Label>
                        <Input
                          id="performanceCarregamento"
                          name="performanceCarregamento"
                          value={formData.performanceCarregamento}
                          onChange={handleChange}
                          placeholder="Ex: Excelente"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="diariasC">Diárias C</Label>
                        <Input
                          id="diariasC"
                          name="diariasC"
                          value={formData.diariasC}
                          onChange={handleChange}
                          placeholder="Ex: 1"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Ocorrências</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="causaAtrazoCarregamento">Causa do Atraso</Label>
                        <Input
                          id="causaAtrazoCarregamento"
                          name="causaAtrazoCarregamento"
                          value={formData.causaAtrazoCarregamento}
                          onChange={handleChange}
                          placeholder="Descreva a causa do atraso"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="numeroOcorrenciaCarga">Nº Ocorrência de Carga</Label>
                        <Input
                          id="numeroOcorrenciaCarga"
                          name="numeroOcorrenciaCarga"
                          value={formData.numeroOcorrenciaCarga}
                          onChange={handleChange}
                          placeholder="Ex: OC001"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Aba Descarga */}
            <TabsContent value="descarga" className="flex-1 overflow-hidden">
              <ScrollArea className="h-full pr-4">
                <div className="space-y-6 pb-4">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Informações de Descarga</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="appClienteDescarga">App Cliente - Descarga</Label>
                        <Input
                          id="appClienteDescarga"
                          name="appClienteDescarga"
                          value={formData.appClienteDescarga}
                          onChange={handleChange}
                          placeholder="Ex: App do cliente"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="statusFinal">Status Final</Label>
                        <Select
                          value={formData.statusFinal}
                          onValueChange={(value) => setFormData(prev => ({ ...prev, statusFinal: value as StatusDescargaEnum }))}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value={StatusDescargaEnum.AGUARDANDO}>{StatusDescargaEnum.AGUARDANDO}</SelectItem>
                            <SelectItem value={StatusDescargaEnum.DESCARREGANDO}>{StatusDescargaEnum.DESCARREGANDO}</SelectItem>
                            <SelectItem value={StatusDescargaEnum.DESCARREGADO}>{StatusDescargaEnum.DESCARREGADO}</SelectItem>
                            <SelectItem value={StatusDescargaEnum.NO_PRAZO}>{StatusDescargaEnum.NO_PRAZO}</SelectItem>
                            <SelectItem value={StatusDescargaEnum.ATRASADO}>{StatusDescargaEnum.ATRASADO}</SelectItem>
                            <SelectItem value={StatusDescargaEnum.CANCELADO}>{StatusDescargaEnum.CANCELADO}</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Aba Financeiro */}
            <TabsContent value="financeiro" className="flex-1 overflow-hidden">
              <ScrollArea className="h-full pr-4">
                <div className="space-y-6 pb-4">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Valores Financeiros</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="freteEmpresaNet">Frete Empresa NET</Label>
                        <Input
                          id="freteEmpresaNet"
                          name="freteEmpresaNet"
                          value={formData.freteEmpresaNet}
                          onChange={handleChange}
                          placeholder="Ex: R$ 2.500,00"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="freteTerceiro">Frete Terceiro</Label>
                        <Input
                          id="freteTerceiro"
                          name="freteTerceiro"
                          value={formData.freteTerceiro}
                          onChange={handleChange}
                          placeholder="Ex: R$ 2.200,00"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="margemTerceiro">Margem Terceiro</Label>
                        <Input
                          id="margemTerceiro"
                          name="margemTerceiro"
                          value={formData.margemTerceiro}
                          onChange={handleChange}
                          placeholder="Ex: 15%"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="freteAgregado">Frete Agregado</Label>
                        <Input
                          id="freteAgregado"
                          name="freteAgregado"
                          value={formData.freteAgregado}
                          onChange={handleChange}
                          placeholder="Ex: R$ 2.000,00"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="margemAgregado">Margem Agregado</Label>
                        <Input
                          id="margemAgregado"
                          name="margemAgregado"
                          value={formData.margemAgregado}
                          onChange={handleChange}
                          placeholder="Ex: 20%"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Resumo por Modalidade</h3>
                    <div className="p-4 border rounded-md bg-muted/30">
                      <div className="text-sm font-medium mb-2">Modalidade Selecionada: {formData.modalidade}</div>
                      <div className="text-xs text-muted-foreground">
                        {formData.modalidade === ModalidadeEnum.FROTA && 'Valores de frete da empresa própria'}
                        {formData.modalidade === ModalidadeEnum.TERCEIRO && 'Valores de frete para terceiros com margem aplicada'}
                        {formData.modalidade === ModalidadeEnum.AGREGADO && 'Valores de frete para agregados com margem aplicada'}
                      </div>
                    </div>
                  </div>
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Aba Documentos */}
            <TabsContent value="documentos" className="flex-1 overflow-hidden">
              <ScrollArea className="h-full pr-4">
                <div className="space-y-6 pb-4">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Documentação Fiscal</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="numeroNotaFiscal">Número da Nota Fiscal</Label>
                        <Input
                          id="numeroNotaFiscal"
                          name="numeroNotaFiscal"
                          value={formData.numeroNotaFiscal}
                          onChange={handleChange}
                          placeholder="Ex: 123456789"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="numeroCTE">Número CTE</Label>
                        <Input
                          id="numeroCTE"
                          name="numeroCTE"
                          value={formData.numeroCTE}
                          onChange={handleChange}
                          placeholder="Ex: 987654321"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="p-4 border border-yellow-200 bg-yellow-50 rounded-md">
                      <div className="text-sm font-medium mb-2 text-yellow-800">⚠️ Documentação Obrigatória</div>
                      <ul className="text-xs text-yellow-700 space-y-1">
                        <li>• CTE é obrigatório para transporte de cargas</li>
                        <li>• Motorista não deve sair sem aguardar a emissão do CTE</li>
                        <li>• Nota Fiscal deve acompanhar a mercadoria</li>
                        <li>• Verificar se todos os documentos estão corretos antes da saída</li>
                      </ul>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Status dos Documentos</h3>
                    <div className="p-4 border rounded-md">
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Nota Fiscal:</span>
                          <span className={formData.numeroNotaFiscal ? 'text-green-600' : 'text-gray-500'}>
                            {formData.numeroNotaFiscal ? '✓ Emitida' : '⏳ Pendente'}
                          </span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>CTE:</span>
                          <span className={formData.numeroCTE ? 'text-green-600' : 'text-yellow-600'}>
                            {formData.numeroCTE ? '✓ Emitido' : '⚠️ Sem CTE'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Aba Observações */}
            <TabsContent value="observacoes" className="flex-1 overflow-hidden">
              <ScrollArea className="h-full pr-4">
                <div className="space-y-6 pb-4">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Observações Gerais</h3>
                    <div className="space-y-2">
                      <Label htmlFor="observacao">Observações</Label>
                      <Textarea
                        id="observacao"
                        name="observacao"
                        value={formData.observacao}
                        onChange={handleChange}
                        placeholder="Digite observações sobre a operação..."
                        className="min-h-[120px]"
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Resumo da Operação</h3>
                    <div className="p-4 border rounded-md bg-muted/30">
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Cliente:</span>
                          <span>{formData.clientePagador || '-'}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Motorista:</span>
                          <span>{formData.motorista || '-'}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Rota:</span>
                          <span>
                            {formData.clienteCidadeOrigem && formData.clienteCidadeDestino
                              ? `${formData.clienteCidadeOrigem}/${formData.ufOrigem} → ${formData.clienteCidadeDestino}/${formData.ufDestino}`
                              : '-'
                            }
                          </span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Modalidade:</span>
                          <span>{formData.modalidade || '-'}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Produto:</span>
                          <span>{formData.produto || '-'}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Peso:</span>
                          <span>{formData.peso || '-'}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>

          <DialogFooter className="pt-4 flex-shrink-0">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancelar
            </Button>
            <Button type="submit">
              {isEditing ? 'Salvar Alterações' : 'Criar Operação'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default OperationDialog;

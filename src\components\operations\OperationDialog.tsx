
import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Operation, ClienteEnum, ModalidadeEnum, StatusCarregamentoEnum } from '@/types';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from '@/hooks/use-toast';

interface OperationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  operation?: Operation;
  onSave: (operation: Operation) => void;
  availableOperators: { id: string; name: string }[];
  availableEquipments: { id: string; model: string }[];
  availableProgrammers: string[];
}

const OperationDialog = ({
  open,
  onOpenChange,
  operation,
  onSave,
  availableOperators,
  availableEquipments,
  availableProgrammers
}: OperationDialogProps) => {
  const { toast } = useToast();
  const isEditing = !!operation;

  // Form state
  const [formData, setFormData] = useState<Partial<Operation>>({
    chave: '',
    dataHoraAgendadaCarregamento: '',
    dataHoraAgendadaDescarga: '',
    produto: '',
    clientePagador: ClienteEnum.PEPSICO,
    idB100SvnDt: '',
    clienteCidadeOrigem: '',
    ufOrigem: '',
    clienteCidadeDestino: '',
    ufDestino: '',
    peso: '',
    motorista: '',
    modalidade: ModalidadeEnum.FROTA,
    diasEmRota: 0,
    modeloEquipamento: '',
    operacaoCadastrada: true,
    programador: ''
  });

  // Initialize form with operation data if editing
  useEffect(() => {
    if (operation) {
      setFormData(operation);
    } else {
      // Reset form for new operation
      setFormData({
        chave: '',
        dataHoraAgendadaCarregamento: '',
        dataHoraAgendadaDescarga: '',
        produto: '',
        clientePagador: ClienteEnum.PEPSICO,
        idB100SvnDt: '',
        clienteCidadeOrigem: '',
        ufOrigem: '',
        clienteCidadeDestino: '',
        ufDestino: '',
        peso: '',
        motorista: '',
        modalidade: ModalidadeEnum.FROTA,
        diasEmRota: 0,
        modeloEquipamento: '',
        operacaoCadastrada: true,
        programador: ''
      });
    }
  }, [operation, open]);

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;

    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? Number(value) : value
    }));
  };

  // Handle operator selection
  const handleOperatorChange = (motorista: string) => {
    setFormData(prev => ({
      ...prev,
      motorista
    }));
  };

  // Handle equipment selection
  const handleEquipmentChange = (modeloEquipamento: string) => {
    setFormData(prev => ({
      ...prev,
      modeloEquipamento
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!formData.chave || !formData.motorista || !formData.clientePagador || !formData.produto) {
      toast({
        title: "Erro de validação",
        description: "Por favor, preencha todos os campos obrigatórios.",
        variant: "destructive"
      });
      return;
    }

    // Generate chave for new operations if not provided
    const operationData: Operation = {
      chave: operation?.chave || `${Math.floor(Math.random() * 1000)}`,
      ...formData as Operation
    };

    // Save the operation
    onSave(operationData);

    // Close the dialog
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Editar Operação' : 'Nova Operação'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6 py-4">
          {/* Identificação */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Identificação</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="chave">Chave *</Label>
                <Input
                  id="chave"
                  name="chave"
                  value={formData.chave}
                  onChange={handleChange}
                  placeholder="Ex: 413"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="clientePagador">Cliente *</Label>
                <Select
                  value={formData.clientePagador}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, clientePagador: value as ClienteEnum }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o cliente" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={ClienteEnum.NOVELIS}>{ClienteEnum.NOVELIS}</SelectItem>
                    <SelectItem value={ClienteEnum.PEPSICO}>{ClienteEnum.PEPSICO}</SelectItem>
                    <SelectItem value={ClienteEnum.AMBEV}>{ClienteEnum.AMBEV}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="idB100SvnDt">ID/B100/SVN/Nº DT</Label>
                <Input
                  id="idB100SvnDt"
                  name="idB100SvnDt"
                  value={formData.idB100SvnDt}
                  onChange={handleChange}
                  placeholder="Ex: DT 11763733"
                />
              </div>
            </div>
          </div>

          {/* Agendamento */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Agendamento</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="dataHoraAgendadaCarregamento">Data/Hora Agendada P/ Carregamento</Label>
                <Input
                  id="dataHoraAgendadaCarregamento"
                  name="dataHoraAgendadaCarregamento"
                  value={formData.dataHoraAgendadaCarregamento}
                  onChange={handleChange}
                  placeholder="05/06/2025 07:00:00"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="dataHoraAgendadaDescarga">Data/Hora Agendada P/ Descarga</Label>
                <Input
                  id="dataHoraAgendadaDescarga"
                  name="dataHoraAgendadaDescarga"
                  value={formData.dataHoraAgendadaDescarga}
                  onChange={handleChange}
                  placeholder="09/06/2025 07:00:00"
                />
              </div>
            </div>
          </div>

          {/* Produto e Rota */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Produto e Rota</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="produto">Produto *</Label>
                <Input
                  id="produto"
                  name="produto"
                  value={formData.produto}
                  onChange={handleChange}
                  placeholder="Ex: PRODUTOS ALIMENTICIOS"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="peso">Peso</Label>
                <Input
                  id="peso"
                  name="peso"
                  value={formData.peso}
                  onChange={handleChange}
                  placeholder="Ex: 16 TON"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="clienteCidadeOrigem">Cidade Origem</Label>
                <Input
                  id="clienteCidadeOrigem"
                  name="clienteCidadeOrigem"
                  value={formData.clienteCidadeOrigem}
                  onChange={handleChange}
                  placeholder="Ex: Cabreúva"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="ufOrigem">UF Origem</Label>
                <Input
                  id="ufOrigem"
                  name="ufOrigem"
                  value={formData.ufOrigem}
                  onChange={handleChange}
                  placeholder="Ex: SP"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="clienteCidadeDestino">Cidade Destino</Label>
                <Input
                  id="clienteCidadeDestino"
                  name="clienteCidadeDestino"
                  value={formData.clienteCidadeDestino}
                  onChange={handleChange}
                  placeholder="Ex: Anápolis"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="ufDestino">UF Destino</Label>
                <Input
                  id="ufDestino"
                  name="ufDestino"
                  value={formData.ufDestino}
                  onChange={handleChange}
                  placeholder="Ex: GO"
                />
              </div>
            </div>
          </div>

          {/* Motorista e Equipamento */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Motorista e Equipamento</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="motorista">Motorista *</Label>
                <Select
                  value={formData.motorista}
                  onValueChange={handleOperatorChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um motorista" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableOperators.map(operator => (
                      <SelectItem key={operator.id} value={operator.name}>
                        {operator.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="modeloEquipamento">Modelo/Equipamento</Label>
                <Select
                  value={formData.modeloEquipamento}
                  onValueChange={handleEquipmentChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o equipamento" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableEquipments.map(equipment => (
                      <SelectItem key={equipment.id} value={equipment.model}>
                        {equipment.model}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="modalidade">Modalidade</Label>
                <Select
                  value={formData.modalidade}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, modalidade: value as ModalidadeEnum }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione a modalidade" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={ModalidadeEnum.FROTA}>{ModalidadeEnum.FROTA}</SelectItem>
                    <SelectItem value={ModalidadeEnum.TERCEIRO}>{ModalidadeEnum.TERCEIRO}</SelectItem>
                    <SelectItem value={ModalidadeEnum.AGREGADO}>{ModalidadeEnum.AGREGADO}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="diasEmRota">Dias em Rota</Label>
                <Input
                  id="diasEmRota"
                  name="diasEmRota"
                  type="number"
                  value={formData.diasEmRota}
                  onChange={handleChange}
                  placeholder="Ex: 4"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="programador">Programador</Label>
                <Select
                  value={formData.programador}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, programador: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o programador" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableProgrammers.map(programmer => (
                      <SelectItem key={programmer} value={programmer}>
                        {programmer}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="placaTracao">Placa/Tração</Label>
                <Input
                  id="placaTracao"
                  name="placaTracao"
                  value={formData.placaTracao}
                  onChange={handleChange}
                  placeholder="Ex: RCH5D99"
                />
              </div>
            </div>
          </div>

          {/* Operação e Códigos */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Operação e Códigos</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="codigoDaOperacao">Código da Operação</Label>
                <Input
                  id="codigoDaOperacao"
                  name="codigoDaOperacao"
                  value={formData.codigoDaOperacao}
                  onChange={handleChange}
                  placeholder="Ex: 308622880"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="numeroRota">Nº Rota</Label>
                <Input
                  id="numeroRota"
                  name="numeroRota"
                  value={formData.numeroRota}
                  onChange={handleChange}
                  placeholder="Número da rota"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="numeroSolicitacaoCarga">Nº Solicitação de Carga</Label>
                <Input
                  id="numeroSolicitacaoCarga"
                  name="numeroSolicitacaoCarga"
                  value={formData.numeroSolicitacaoCarga}
                  onChange={handleChange}
                  placeholder="Ex: 139563"
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="operacaoCadastrada"
                checked={formData.operacaoCadastrada}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, operacaoCadastrada: checked as boolean }))}
              />
              <Label htmlFor="operacaoCadastrada">Operação Cadastrada?</Label>
            </div>
          </div>

          <DialogFooter className="pt-4">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancelar
            </Button>
            <Button type="submit">
              {isEditing ? 'Salvar Alterações' : 'Criar Operação'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default OperationDialog;
